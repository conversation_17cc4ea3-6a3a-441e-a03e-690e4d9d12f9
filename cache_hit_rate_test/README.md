# SGLang 缓存命中率测试

本目录包含了对SGLang集群进行缓存命中率测试的完整实验框架。

## 目录结构

```
cache_hit_rate_test/
├── scripts/           # 测试脚本
│   ├── collect_metrics.py    # Prometheus指标采集
│   ├── api_tester.py         # API测试脚本
│   ├── run_experiment.py     # 完整实验执行器
│   └── analyze_results.py    # 结果分析脚本
├── data/              # 测试数据和指标
│   ├── baseline_metrics.json
│   ├── scenario_*_metrics.json
│   └── scenario_*_results.json
├── logs/              # 测试日志
├── reports/           # 实验报告
│   ├── experiment_summary.md    # 实验总结
│   └── experiment_report.md     # 详细分析报告
└── README.md          # 本文件
```

## 测试场景

1. **场景1: 完全随机请求负载** - 测试缓存未命中情况
2. **场景2: 完全相同请求负载** - 测试缓存命中情况  
3. **场景3: 相似请求负载** - 测试部分缓存命中情况
4. **场景4: 多轮对话场景** - 测试真实对话场景

## 关键发现

🎯 **最重要发现**: 场景2中sglang-prefill-1达到了**94.74%的缓存命中率**

📊 **主要结论**:
- SGLang的前缀缓存系统在完全相同请求下表现优异
- 轮询策略会影响缓存分布，建议考虑cache_aware策略
- 缓存需要精确匹配，相似请求无法命中缓存
- 多轮对话由于内容不断变化，缓存效果有限

## 使用方法

### 单独运行脚本

```bash
# 收集当前指标
python3 scripts/collect_metrics.py

# 执行特定场景测试
python3 scripts/api_tester.py --scenario 1 --requests 15 --concurrent 3

# 分析结果
python3 scripts/analyze_results.py
```

### 运行完整实验

```bash
python3 scripts/run_experiment.py
```

## 配置要求

- SGLang集群已部署并运行
- Router通过port-forward暴露在 http://127.0.0.1:8000
- Prometheus通过port-forward暴露在 http://127.0.0.1:9090
- Python 3.x 环境，需要 requests 库

## 报告文件

- `reports/experiment_summary.md` - 实验概览和关键发现
- `reports/experiment_report.md` - 详细的分析报告，包含所有指标变化

## 测试参数

- **模型**: /models/Qwen3-32B
- **max_tokens**: 30
- **temperature**: 0.7
- **top_p**: 0.9
- **并发数**: 3
- **每场景请求数**: 15 (场景4为20个请求，5个对话×4轮)

---

*实验完成时间: 2025-09-23*
